<route lang="json5">
{
  style: {
    navigationBarTitleText: '收货地址编辑',
  },
}
</route>
<script lang="ts" setup>
import { storeToRefs } from 'pinia'
import { useUserStore } from '@/store/user'
import { Article } from '@/enums'
import PaidDisclaimer from '@/components/agreement/paidDisclaimer.vue'
import { addressStore } from '@/store/addressStore'
import { useAddressInfoHook } from '@/components/addressPage/addressInfoHook'
import { addressCreateApi, addressUpdateApi } from '@/service/addressPageApi'
import { msgModalStore } from '@/store/msgModalStore'
import { Color } from '@/enums/colorEnum'

const useMsgModalStore = msgModalStore()

const userStore = useUserStore()
const { userId } = storeToRefs(userStore)
const useAddressStore = addressStore()
const { addressData, addressId, selectAddressId } = storeToRefs(useAddressStore)

const operationType = ref('add')
const agree = ref(false)
const isModalShow = ref(false)

const { model, form, rules, validatePhoneNumber } = useAddressInfoHook()

onLoad((option: any) => {
  operationType.value = option.type
  if (option.type === 'edit') {
    model.addressDetail = addressData.value.addressDetail
    model.district = addressData.value.district
    model.isDefault = addressData.value.isDefault
    model.phone = addressData.value.phone
    model.realName = addressData.value.realName
  }
})

const toBack = () => {
  uni.showToast({
    title: '保存成功',
  })
  setTimeout(() => {
    uni.navigateBack()
  }, 1000)
}

const handleSubmit = () => {
  form.value
    .validate()
    .then((valid) => {
      console.log('valid', valid)
      if (valid) {
        if (agree.value) {
          switch (operationType.value) {
            case 'add':
              addressCreateApi({
                addressDetail: model.addressDetail,
                district: model.district,
                isDefault: model.isDefault,
                phone: model.phone,
                realName: model.realName,
                userId: userId.value,
              }).then(() => {
                toBack()
              })
              break
            case 'edit':
              addressUpdateApi({
                addressDetail: model.addressDetail,
                district: model.district,
                isDefault: model.isDefault,
                phone: model.phone,
                realName: model.realName,
                userId: userId.value,
                addressId: addressId.value,
              }).then(() => {
                toBack()
              })
              break
          }
        } else {
          useMsgModalStore
            .confirm({
              title: '温馨提示',
              content: '请先勾选已阅读并同意《用户服务协议》《隐私政策》',
            })
            .then(() => {
              uni.pageScrollTo({
                selector: '#agreeElement',
              })
            })
        }
      }
    })
    .catch((error) => {
      console.log(error, 'error')
    })
}

const addressInfo = ref({
  address: '',
  latitude: 0,
  longitude: 0,
})

const selectAddress = () => {
  // 点击调起地图选择位置
  uni.authorize({
    scope: 'scope.userLocation',
    success(res) {
      console.log('scope.userLocation获得授权', res)
      // 选择位置
      uni.chooseLocation({
        success: function (res) {
          console.log('选择地点成功', res)
          console.log('位置名称：' + res.name)
          console.log('详细地址：' + res.address)
          console.log('纬度：' + res.latitude)
          console.log('经度：' + res.longitude)
          addressInfo.value.address = res.address
          addressInfo.value.latitude = res.latitude
          addressInfo.value.longitude = res.longitude
        },
        fail(error) {
          console.log('选择位置失败', error)
        },
      })
    },
  })
}

const handleModalOk = () => {
  agree.value = true
  isModalShow.value = false
}
</script>
<template>
  <up-modal
    :show="isModalShow"
    confirmText="同意"
    showCancelButton
    closeOnClickOverlay
    @confirm="handleModalOk"
    @cancel="isModalShow = false"
    @close="isModalShow = false"
  >
    <PaidDisclaimer :articleIds="[Article.privacy]" />
  </up-modal>
  <view class="p-4 pb-6">
    <!--    <view class="uni-form-item uni-column address address-arrow">
      <view class="title">地址</view>
      <view class="operation" @click="selectAddress">
        <input
          v-model="addressInfo.address"
          class="uni-input"
          name="address"
          placeholder="请选择地址   "
          placeholder-class="address-pla"
        />
      </view>
    </view>-->
    <view class="bg-white rounded overflow-hidden pl-4">
      <up-form labelWidth="100" class="mt-2" ref="form" :model="model" :rules="rules">
        <up-form-item label="省市区" prop="district" required>
          <up-input border="bottom" clearable v-model="model.district" placeholder="省市区" />
        </up-form-item>
        <up-form-item label="详细地址与门牌号" prop="addressDetail" required>
          <up-input
            border="bottom"
            clearable
            v-model="model.addressDetail"
            placeholder="详细地址与门牌号"
          />
        </up-form-item>
        <up-form-item label="姓名" prop="realName" required>
          <up-input border="bottom" clearable v-model="model.realName" placeholder="姓名" />
        </up-form-item>
        <up-form-item label="联系手机号" prop="phone" required>
          <up-input
            border="bottom"
            type="number"
            :maxlength="11"
            clearable
            v-model="model.phone"
            placeholder="联系手机号"
          >
            <template #suffix>
              <text class="text-gray text-xs">{{ model.phone.length }}/11</text>
            </template>
          </up-input>
        </up-form-item>
        <view class="flex justify-between items-center p-4">
          <view class="text-sm">设为默认</view>
          <up-switch v-model="model.isDefault" :activeColor="Color.primary" />
        </view>
      </up-form>
    </view>
  </view>
  <view id="agreeElement" class="flex justify-center items-center text-xs">
    <up-checkbox
      usedAlone
      v-model:checked="agree"
      labelSize="12"
      :size="14"
      :activeColor="Color.primary"
      label="我已阅读并同意"
    ></up-checkbox>
    <view class="o-color-primary" @click.stop="isModalShow = true">《平台用户服务协议》</view>
  </view>
  <view class="box-border fixed w-full p-4 left-0 bottom-0 z-10">
    <view
      class="p-3 flex-grow-1 o-bg-primary flex items-center justify-center color-white font-bold rounded"
      @click="handleSubmit"
    >
      保存
    </view>
  </view>
</template>

<style lang="scss" scoped></style>
