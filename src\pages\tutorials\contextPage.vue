<route lang="json5">
{
  style: {
    navigationBarTitleText: '文章内容',
  },
}
</route>
<script lang="ts" setup>
import { articleLoadApi, ArticleLoadRes } from '@/service/tutorialsApi'
import { PublicImgPath } from '@/components/image'

const articleUrl = ref('')
const isUrl = ref(false)
const content = ref('')
const title = ref('')
const topImg = ref('')
const id = ref(0)
const DEFAULT_IMG = 'https://app.xunma.store/images/p_index_chip_green_new.png'

const { loading, error, data, run } = useRequest<ArticleLoadRes>(() =>
  articleLoadApi({
    articleId: id.value,
  }),
)

onLoad((option) => {
  id.value = Number(option.id)
  run().then(() => {
    if (data.value.articleUrl) {
      articleUrl.value = data.value.articleUrl
      isUrl.value = true
    } else {
      // 给视频适配宽度和高度
      const r = updateVideoDimensions(data.value.articleConent)
      // 添加默认行高
      content.value = addLineHeight(r)
      title.value = data.value.articleTitle
      topImg.value = data.value.imageUrl || DEFAULT_IMG
    }
  })
})

// 使用正则表达式匹配 <body> 标签，并且替换为带有 style 属性的版本
const addLineHeight = (articleContent: string) => {
  return articleContent.replace(
    /<body(?!.*\bstyle=)/i, // 查找 <body 并确保后面没有 style 属性
    '<body style="line-height:1.5em;"', // 替换为带有 style 属性的 <body
  )
}

const updateVideoDimensions = (articleContent: string) => {
  // 正则表达式用于查找 <video> 标签，并捕获 width 和 height 属性
  const videoRegex = /<video[^>]*width="[^"]*"[^>]*height="[^"]*"[^>]*>/gi

  // 替换函数，它会将匹配到的 <video> 标签中的 width 和 height 更新为目标值
  const replaceFn = (match: string) => {
    return match
      .replace(/width="[^"]*"/, 'width="100%"')
      .replace(/height="[^"]*"/, 'height="200px"')
  }

  // 使用正则表达式和替换函数来更新 articleContent 中的所有 <video> 标签
  return articleContent.replace(videoRegex, replaceFn)
}
</script>
<template>
  <web-view v-if="isUrl" :src="articleUrl" />
  <view v-else class="f-page bg-white">
    <view v-if="loading" class="p-6">
      <up-skeleton rows="3" title loading :animate="true"></up-skeleton>
    </view>
    <view v-else>
      <image class="w-full" mode="widthFix" :src="topImg"></image>
      <view class="px-6">
        <view
          class="px-6 pt-6 text-center text-xl font-bold"
          style="word-break: break-all; line-break: anywhere"
        >
          {{ title }}
        </view>
        <up-divider />
        <up-parse :content="content"></up-parse>
        <view class="f-divider mt-16 mb-4 ml-auto mr-auto">
          <up-divider />
        </view>
        <view class="w-full center">
          <view class="f-app-logo flex items-center gap-1 px-4 py-1 rounded">
            <image
              :src="PublicImgPath + 'logo-white.svg'"
              alt="迅码智联"
              mode="heightFix"
              style="height: 6vw"
            />
            <view class="f-tag-card h-fit text-white font-bold px-1 py-0.5 rounded text-2xs">
              智联
            </view>
          </view>
        </view>
        <view class="p-6"></view>
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.f-page {
  min-height: 100vh;
}

.f-divider {
  width: 60%;
}

.f-app-logo {
  background: linear-gradient(90deg, #fa4c03 0%, #ff5b00 48.56%, #fd7424 100%);
}
.f-tag-card {
  background: linear-gradient(94deg, #181818 49.56%, #555 80.48%, #181818 98.01%);
}
</style>
