<!-- 使用 type="home" 属性设置首页，其他页面不需要设置，默认为page；推荐使用json5，更强大，且允许注释 -->
<route lang="json5">
{
  style: {
    navigationBarTitleText: '订单开票',
    enablePullDownRefresh: true,
    backgroundColor: '#f0f3f8',
  },
}
</route>
<script lang="ts" setup>
import PriceBox from '@/components/Price/PriceBox.vue'
import { invoiceOrderStore } from '@/store/invoiceOrderStore'
import { storeToRefs } from 'pinia'
import { allOrderPageApi, AllOrderPageRes, AllOrderPageResData } from '@/service/orderApi'
import { OrderDirection } from '@/enums/httpEnum'
import { useUserStore } from '@/store/user'
import { getServerTypeStr, InvoiceState, PayState, ServerType } from '@/enums'
import { makeFilmAndReportServiceDescription } from '@/components/descriptionStr'
import { Color } from '@/enums/colorEnum'

const userStore = useUserStore()
const { userId } = storeToRefs(userStore)

const useInvoiceOrderStore = invoiceOrderStore()
const { orderObjList, orderSuccess } = storeToRefs(useInvoiceOrderStore)
const showEmptyIcon = ref(false)
const orderCode = ref([])
const page = ref(1)
const list = ref<AllOrderPageResData[]>([])
// const selectValue = ref<AllOrderPageResData[]>([])
const filterList = [{ name: '全部' }, { name: '未开票' }, { name: '已开票' }]
const filterCurrent = ref(0)

const init = () =>
  new Promise((resolve, reject) => {
    list.value = []
    orderCode.value = []
    orderObjList.value = []
    run()
      .then(() => {
        list.value = [...data.value]
        resolve(true)
      })
      .catch((err) => {
        reject(err)
      })
  })

onLoad(() => {
  // 页面加载,清空orderCode，后退到此页面时，不触发onLoad
  init()
})

onShow(() => {
  // 订单成功后退触发
  if (orderSuccess.value) {
    orderSuccess.value = false
    page.value = 1
    init()
  }
})

// 下拉刷新
onPullDownRefresh(() => {
  showEmptyIcon.value = false
  page.value = 1
  init().finally(() => {
    uni.stopPullDownRefresh()
  })
})

const getIsInvoiced = () => {
  switch (filterCurrent.value) {
    case 0:
      return null
    case 1:
      return 0
    case 2:
      return 1
    default:
      return null
  }
}

const { loading, error, data, run } = useRequest<AllOrderPageRes>(() =>
  allOrderPageApi({
    // serverType:
    isInvoiced: getIsInvoiced(),
    userId: userId.value,
    payState: PayState.paid,
    groupBy: '',
    needTotalCount: true,
    // orderBy: 'payDate',
    orderDirection: OrderDirection.desc,
    pageIndex: page.value,
    pageSize: 50,
  }),
)

// 滚到页面底部加载更多
onReachBottom(() => {
  if (!showEmptyIcon.value) {
    page.value++
    run().then(() => {
      list.value = [...list.value, ...data.value]
      if (data.value.length === 0) {
        showEmptyIcon.value = true
      }
    })
  }
})

const handleSelect = (d: AllOrderPageResData) => {
  // 发票状态，1：已开票，0：未申请,2：已申请,-1:废票
  if (!d.invoiceState) {
    if (orderCode.value.includes(d.orderCode)) {
      orderCode.value = orderCode.value.filter((item) => item !== d.orderCode)
      // selectValue.value = selectValue.value.filter((item) => item.orderCode !== d.orderCode)
    } else {
      orderCode.value.push(d.orderCode)
      // selectValue.value.push(d)
    }
  }
}

const getStateColor = (status: InvoiceState) => {
  switch (status) {
    case InvoiceState.failed:
      return 'color-red'
    case InvoiceState.applying:
      return 'color-primary'
    case InvoiceState.done:
      return 'color-green'
    default:
      return 'text-gray'
  }
}

const handleSubmit = () => {
  if (orderCode.value.length > 0) {
    const length = orderCode.value.length
    let price = 0
    const selectValue = list.value.filter((item) => orderCode.value.includes(item.orderCode))
    selectValue.forEach((subItem) => {
      price += subItem.actuallyPrice
    })
    price = Number(price.toFixed(2))
    orderObjList.value = selectValue.map((item) => {
      return {
        orderId: item.orderId,
        serverType: item.serverType,
      }
    })
    uni.navigateTo({
      url: '/pages/myOrder/invoiceInfo?length=' + length + '&price=' + price,
    })
  }
}

const getDisable = (invoiceState: number) => {
  return invoiceState !== 0 && invoiceState !== null
}

const handleFilter = () => {
  page.value = 1
  init()
}
// TODO 下拉刷新用scrollView
// TODO 检查所有页面，当<up-checkbox>没用v-model时，点击slot内容触发，有没有跟点击框，割裂不同步值
// TODO 每个提交功能，是否有做防止重复点击提交
</script>
<template>
  <up-sticky bgColor="#fff">
    <up-tabs
      :lineColor="Color.primary"
      :lineWidth="90"
      :scrollable="false"
      :list="filterList"
      v-model:current="filterCurrent"
      @change="handleFilter"
    ></up-tabs>
  </up-sticky>
  <view class="px-4 pb-10">
    <up-checkbox-group placement="column" v-model="orderCode">
      <view
        class="bg-white p-4 rounded mt-3"
        v-for="item in list"
        :key="`${item.serverType}${item.orderId}`"
        :class="getDisable(item.invoiceState) ? 'o-bg-white-disable' : ''"
      >
        <view class="flex items-center" @click="handleSelect(item)">
          <up-checkbox :name="item.orderCode" :disabled="!!item.invoiceState"></up-checkbox>
          <view class="flex-grow-1">
            <view class="o-color-aid text-xs">成交时间：{{ item.payDate }}</view>
            <view class="o-color-aid text-xs">订单编号：{{ item.orderCode }}</view>
            <!--          <view class="o-color-aid text-xs">订单类型：{{ getServerTypeStr(item.serverType) }}</view>-->
          </view>
          <view
            v-if="getDisable(item.invoiceState)"
            class="font-bold"
            :class="getStateColor(item.invoiceState)"
          >
            {{ item.invoiceStateStr }}
          </view>
        </view>
        <view class="o-line mt-2 mb-2"></view>
        <view class="font-bold mb-1">{{ getServerTypeStr(item.serverType) }}：</view>
        <view v-if="item.serverType === ServerType.miniShop">
          {{ item.qrOrderParamDTO?.tempName }}
        </view>
        <view
          v-if="
            [
              ServerType.infoReport,
              ServerType.designServer,
              ServerType.labelPrint,
              ServerType.registerService,
              ServerType.modifyService,
              ServerType.renewalService,
              ServerType.importedGoods,
            ].includes(item.serverType)
          "
        >
          {{ item.otherOrderParamDTO?.orderContent }}
        </view>
        <view
          v-if="[ServerType.makeFilm, ServerType.storeCode].includes(item.serverType)"
          class="flex items-center mb-2 o-row-scroll"
        >
          <view class="o-barcode-gray-card rounded">{{ item.barOrderParamDTO?.startBarCode }}</view>
          <template
            v-if="item.barOrderParamDTO?.endBarCode !== item.barOrderParamDTO?.startBarCode"
          >
            <view>~</view>
            <view class="o-barcode-gray-card rounded">{{ item.barOrderParamDTO?.endBarCode }}</view>
          </template>
        </view>
        <view class="flex justify-between items-end gap-3">
          <view
            class="text-gray text-xs"
            v-if="[ServerType.makeFilm, ServerType.storeCode].includes(item.serverType)"
          >
            <view class="">放大系数为：{{ item.barOrderParamDTO?.size }}</view>
            <view>
              {{ item.number }}张
              <text>×</text>
              {{ item.price }}元/张
            </view>
            <view v-if="item.isHasOtherServer">含{{ makeFilmAndReportServiceDescription }}</view>
          </view>
          <view v-if="item.serverType === ServerType.infoReport" class="text-xs text-gray">
            {{ item.price }}元/批
          </view>
          <view v-else></view>
          <view class="flex shrink-0 justify-end">
            <view class="flex items-baseline">
              <view class="text-sm">实付：</view>
              <price-box :price="item.actuallyPrice" :size="36" />
            </view>
          </view>
        </view>
      </view>
    </up-checkbox-group>
    <up-empty
      v-if="list?.length === 0"
      icon="https://app.xunma.store/images/common/search.png"
      text="暂无订单"
    ></up-empty>
  </view>
  <view class="w-full o-color-aid text-center" v-if="showEmptyIcon">- 已经到底了 -</view>
  <view class="p-10"></view>
  <view class="box-border fixed w-full p-4 left-0 bottom-0 z-10">
    <view
      class="p-3 flex-grow-1 flex items-center justify-center color-white font-bold rounded"
      :class="orderCode.length > 0 ? 'o-bg-primary' : 'o-bg-primary-disable'"
      @click="handleSubmit"
    >
      {{ orderCode.length > 1 ? '合并开票' : '开票' }}
    </view>
  </view>
</template>

<style lang="scss" scoped></style>
