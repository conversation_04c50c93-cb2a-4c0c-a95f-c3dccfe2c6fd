<script lang="ts" setup>
import {
  barcodePageApi,
  BarCodePageRes,
  downloadBarCodeApi,
  sizeListApi,
  vendorCodeListApi,
} from '@/service/barcodePageApi'
import { OrderDirection } from '@/enums/httpEnum'
import { useUserStore } from '@/store/user'
import { storeToRefs } from 'pinia'
import { BarType } from '@/enums'
import BackTop from '@/pages/about/components/backTop/backTop.vue'
import { useScrollViewBackTop } from '@/pages/about/components/backTop/useScrollViewBackTop'
import { msgModalStore } from '@/store/msgModalStore'
import { Color } from '@/enums/colorEnum'
import { CheckGroupChangeType } from '@/types/myType'
import { validateMail } from '@/utils/tool'

const userStore = useUserStore()
const { userId, downloadEmail } = storeToRefs(userStore)

const useMsgModalStore = msgModalStore()

const barCode = ref('')
const coefficient = ref('')
const isShowFilter = ref(false)
const isModalShow = ref(false)
const isFilter = ref(false)
const isSelectAll = ref(false)
const isShowVendorCodePick = ref(false)
const isShowSizePick = ref(false)
const isLoading = ref(false)
const barTypeArr = ref<string[]>([])
const vendorCode = ref('')
const selectValue = ref<any>([])
// 当前下拉刷新状态，true 表示下拉刷新已经被触发，false 表示下拉刷新未被触发
const refreshTriggered = ref(false)
const page = ref(1)
const pageSize = 500
const formModel = reactive({
  email: downloadEmail.value,
})

const { scrollTop, flag, getScroll, getToTop } = useScrollViewBackTop()

onMounted(() => {
  userStore.login().then(() => {
    run()
  })
})

const onRefresh = () => {
  if (refreshTriggered.value) return
  page.value = 1
  refreshTriggered.value = true
  run().finally(() => {
    refreshTriggered.value = false
  })
}

const getBarType = (arr) => {
  if (arr.length > 1) {
    return ''
  } else {
    if (arr.includes(BarType.EAN13)) {
      return BarType.EAN13
    } else if (arr.includes(BarType.ITF14)) {
      return BarType.ITF14
    }
  }
  return ''
}

const arrToStr = (d: any) => {
  if (typeof d !== 'string') {
    return ''
  } else return d
}

const isShowCoefficientClear = (d: any) => {
  if (d === '') {
    return false
  } else return !Array.isArray(d)
}

const { loading, error, data, pageIndex, totalCount, totalPages, run } = useRequest<BarCodePageRes>(
  () =>
    barcodePageApi({
      barCode: barCode.value,
      barType: getBarType(barTypeArr.value),
      size: Number(coefficient.value),
      userId: userId.value,
      vendorCode: arrToStr(vendorCode.value),
      groupBy: '',
      needTotalCount: true,
      orderBy: 'barCode',
      orderDirection: OrderDirection.asc,
      pageIndex: page.value,
      pageSize,
    }),
)

const transformSize = (d: any) => {
  const r = d.map((item: any) => item.size)
  r.splice(r.indexOf(null), 1)
  // 将r从小到大排序
  return [r.sort((a, b) => a - b)]
}

const transformVendorCode = (d: any) => {
  const r = d.map((item: any) => item.vendorCode)
  // 去掉r中为null的项
  r.splice(r.indexOf(null), 1)
  return [r.sort((a, b) => a - b)]
}

const {
  loading: sizeLoading,
  data: sizeData,
  run: sizeRun,
} = useRequest(() => sizeListApi({ userId: userId.value }), { transform: transformSize })

const {
  loading: vendorCodeLoading,
  data: vendorCodeData,
  run: vendorCodeRun,
} = useRequest(() => vendorCodeListApi({ userId: userId.value }), {
  transform: transformVendorCode,
})

watch(isSelectAll, () => {
  if (isSelectAll.value) {
    selectValue.value = data.value.map((item) => item.barCodeId)
  } else {
    selectValue.value = []
  }
})

const handleSubmit = () => {
  if (selectValue.value.length > 0) {
    isModalShow.value = true
  }
}

const handleSearch = () => {
  isSelectAll.value = false
  selectValue.value = []
  run()
  isShowFilter.value = false
}

const clearVendorCode = () => {
  vendorCode.value = ''
  isShowVendorCodePick.value = false
}

const clearCoefficient = () => {
  coefficient.value = ''
  isShowSizePick.value = false
}

const handleReset = () => {
  selectValue.value = []
  barCode.value = ''
  barTypeArr.value = []
  vendorCode.value = ''
  coefficient.value = ''
  run()
  isShowFilter.value = false
}

watch([coefficient, vendorCode, barTypeArr], () => {
  isFilter.value =
    coefficient.value !== '' || vendorCode.value !== '' || barTypeArr.value.length > 0
})

watch(loading, () => {
  if (loading.value) {
    uni.showLoading({
      title: '加载中',
    })
  } else {
    uni.hideLoading()
  }
})

const handlePageChange = () => {
  isSelectAll.value = false
  selectValue.value = []
  run()
}

const handleModalOk = () => {
  if (isLoading.value) return
  if (formModel.email === '') {
    uni.showToast({
      icon: 'none',
      title: '请输入邮箱地址',
    })
  } else if (!validateMail(formModel.email)) {
    uni.showToast({
      icon: 'none',
      title: '请输入正确的邮箱地址',
    })
  } else {
    isLoading.value = true
    uni.showLoading({ title: '提交申请中...' })
    downloadBarCodeApi({
      barCodeIdList: selectValue.value,
      downloadType: 2,
      email: formModel.email,
      userId: userId.value,
    })
      .then(() => {
        downloadEmail.value = formModel.email
        uni.hideLoading()
        isModalShow.value = false
        useMsgModalStore.alert({
          title: '已安排发送到邮箱',
          content:
            '鉴于条码制作需要时间，且邮件收发速度受网络影响，如果长时间没有收到邮件，请再提交申请，或咨询客服。谢谢！',
        })
      })
      .catch(() => {
        uni.hideLoading()
      })
      .finally(() => {
        isLoading.value = false
      })
  }
}

const vendorCodeConfirm = (e: any) => {
  vendorCode.value = e.value[0]
  isShowVendorCodePick.value = false
}

const showVendorCodePick = () => {
  isShowVendorCodePick.value = true
  vendorCodeRun()
}

const sizeConfirm = (e: any) => {
  coefficient.value = e.value[0]
  isShowSizePick.value = false
}

const showSizePick = () => {
  isShowSizePick.value = true
  sizeRun()
}
const handlePreviousPage = () => {
  if (page.value !== 1) {
    page.value--
    handlePageChange()
  }
}
const handleNextPage = () => {
  if (page.value >= totalPages.value) {
    page.value = totalPages.value
  } else {
    page.value++
    handlePageChange()
  }
}

const barTypeCheckboxChange = (e: CheckGroupChangeType) => {
  barTypeArr.value = e.detail.value
}

const isSelectAllCheckboxChange = (e: CheckGroupChangeType) => {
  isSelectAll.value = e.detail.value.length !== 0
}

const selectValueCheckboxChange = (e: CheckGroupChangeType) => {
  selectValue.value = e.detail.value.map((item) => Number(item))
}
</script>
<template>
  <up-modal
    :show="isModalShow"
    closeOnClickOverlay
    showCancelButton
    title="条码将以邮件形式发送"
    @cancel="isModalShow = false"
    @close="isModalShow = false"
    @confirm="handleModalOk"
  >
    <up-input v-model="formModel.email" border="bottom" placeholder="请输入接收邮箱"></up-input>
  </up-modal>
  <view class="py-2 relative z-1 bg-white">
    <view class="box-border fixed w-full p-4 left-0 bottom-0 z-10">
      <view
        :class="selectValue.length > 0 ? 'o-bg-primary' : 'o-bg-primary-disable'"
        class="p-3 flex-grow-1 flex items-center justify-center color-white font-bold rounded"
        @click="handleSubmit"
      >
        批量下载
      </view>
    </view>
    <view v-if="isShowFilter" class="f-overlay" @click="isShowFilter = false" />
    <view class="o-bg-no mx-4 flex items-center gap-2 py-1 pl-4 pr-3" style="border-radius: 2rem">
      <up-input
        v-model="barCode"
        :maxlength="14"
        border="none"
        class="grow o-bg-transparent"
        clearable
        placeholder="条码编码"
        type="number"
      ></up-input>
      <up-icon name="search" :size="20" @click="handleSearch"></up-icon>
      <view
        :class="isFilter ? 'o-color-primary' : 'o-color-aid'"
        class="text-sm flex items-center gap-1"
        @click="isShowFilter = !isShowFilter"
      >
        <view class="shrink-0">筛选</view>
        <view style="min-width: 1.5rem">
          <up-icon v-if="isShowFilter" name="arrow-up" :size="14"></up-icon>
          <up-icon v-else name="arrow-down" :size="14"></up-icon>
        </view>
      </view>
    </view>
    <view class="relative">
      <view v-if="isShowFilter" class="bg-white p-4 absolute left-0 w-full box-border z-10">
        <view class="f-form-item">
          <view class="f-label">条码类型</view>
          <!--          <up-checkbox-group v-model="barTypeArr" class="grow-1">
            <up-checkbox :name="BarType.EAN13" label="EAN-13"></up-checkbox>
            <text class="px-2"></text>
            <up-checkbox :name="BarType.ITF14" label="ITF-14"></up-checkbox>
          </up-checkbox-group>-->
          <checkbox-group class="grow-1 flex" @change="barTypeCheckboxChange">
            <label class="flex items-center mr-2">
              <checkbox
                :color="Color.primary"
                style="transform: scale(0.7)"
                :value="BarType.EAN13"
                :checked="barTypeArr.includes(BarType.EAN13)"
              />
              <text>EAN-13</text>
            </label>
            <label class="flex items-center">
              <checkbox
                :color="Color.primary"
                style="transform: scale(0.7)"
                :value="BarType.ITF14"
                :checked="barTypeArr.includes(BarType.ITF14)"
              />
              <text>ITF-14</text>
            </label>
          </checkbox-group>
        </view>
        <view class="f-form-item" @click="showVendorCodePick">
          <view class="f-label">厂商识别码</view>
          <up-input
            v-model="vendorCode"
            border="bottom"
            class="grow-1"
            disabled
            disabledColor="#fff"
            placeholder="请选择厂商识别码"
          ></up-input>
          <template #right>
            <up-icon name="arrow-right" :size="14"></up-icon>
          </template>
        </view>
        <up-picker
          :columns="vendorCodeData"
          :loading="vendorCodeLoading"
          :show="isShowVendorCodePick"
          :confirmColor="Color.primary"
          cancelText="清空"
          closeOnClickOverlay
          title="厂商识别码"
          @cancel="clearVendorCode"
          @close="isShowVendorCodePick = false"
          @confirm="vendorCodeConfirm"
        />
        <view class="f-form-item" @click="showSizePick">
          <view class="f-label">放大系数</view>
          <up-input
            v-model="coefficient"
            border="bottom"
            class="grow-1"
            disabled
            disabledColor="#ffffff"
            placeholder="请选择放大系数"
          ></up-input>
          <template #right>
            <up-icon name="arrow-right" :size="14"></up-icon>
          </template>
        </view>
        <up-picker
          :columns="sizeData"
          :loading="sizeLoading"
          :show="isShowSizePick"
          :confirmColor="Color.primary"
          cancelText="清空"
          closeOnClickOverlay
          title="放大系数"
          @cancel="clearCoefficient"
          @close="isShowSizePick = false"
          @confirm="sizeConfirm"
        />
        <view class="flex gap-2 mt-3">
          <view
            class="p-2 flex-grow-1 flex items-center o-border justify-center rounded"
            @click="handleReset"
          >
            重置
          </view>
          <view
            class="p-2 flex-grow-2 flex items-center o-bg-primary justify-center color-white rounded"
            @click="handleSearch"
          >
            筛选
          </view>
        </view>
      </view>
    </view>
    <view class="flex mt-2 mx-4">
      <checkbox-group @change="isSelectAllCheckboxChange">
        <checkbox
          :color="Color.primary"
          style="transform: scale(0.7)"
          value="0"
          :checked="isSelectAll"
        />
      </checkbox-group>
      <view class="ml-6 flex justify-between">
        <view class="f-table-t font-bold">条码编码</view>
        <view class="f-table-c font-bold">放大系数</view>
      </view>
    </view>
  </view>
  <scroll-view
    :refresher-enabled="true"
    :refresher-triggered="refreshTriggered"
    :scroll-top="scrollTop"
    :scroll-with-animation="true"
    class="f-scroll-box o-bg-no relative"
    :scroll-y="true"
    @refresherrefresh="onRefresh"
    @scroll="getScroll"
  >
    <checkbox-group @change="selectValueCheckboxChange">
      <label
        class="flex items-center"
        :class="selectValue.includes(item.barCodeId) ? 'bg-white py-2 px-4' : 'py-2 px-4'"
        v-for="item in data"
        :key="item.barCodeId"
      >
        <checkbox
          :color="Color.primary"
          style="transform: scale(0.7)"
          :value="item.barCodeId.toString()"
          :checked="selectValue.includes(item.barCodeId)"
        />
        <view class="ml-6 flex justify-between">
          <view class="f-table-t">{{ item.barCode }}</view>
          <view class="f-table-c">{{ item.size }}</view>
        </view>
      </label>
    </checkbox-group>
    <up-empty
      v-if="error || data?.length === 0"
      icon="https://app.xunma.store/images/common/search.png"
      text="当前搜索无结果"
    ></up-empty>
    <view v-if="totalPages > 1" class="flex text-sm p-4 justify-center items-center gap-4">
      <up-icon
        :color="page === 1 ? Color.disabledColor : Color.inputColor"
        class="p-4"
        name="arrow-left"
        :size="14"
        @click="handlePreviousPage"
      ></up-icon>
      <view>{{ page }} / {{ totalPages }}</view>
      <up-icon
        :color="page >= totalPages ? Color.disabledColor : Color.inputColor"
        class="p-4"
        name="arrow-right"
        :size="14"
        @click="handleNextPage"
      ></up-icon>
    </view>
    <view class="p-10"></view>
  </scroll-view>
  <back-top v-if="flag" @tap="getToTop" />
</template>

<style lang="scss" scoped>
.f-form-item {
  @apply flex items-center;
}

.f-label {
  @apply mr-4;
  min-width: 5rem;
}

.f-scroll-box {
  height: calc(100vh - 8.1rem);
}

.f-table-t {
  width: 400rpx;
}

.f-table-c {
  width: 150rpx;
}

.f-overlay {
  position: absolute;
  top: 4rem;
  left: 0;
  z-index: 10;
  width: 100%;
  height: calc(100vh - calc(44px + env(safe-area-inset-top)));
  background-color: rgba(0, 0, 0, 0.7);
  transition-duration: 300ms;
}
</style>
